#!/usr/bin/env node

/**
 * 综合图片数据一致性检查和修复脚本
 * 
 * 功能包括：
 * 1. 数据关联性检查：Product表与Image表的一致性
 * 2. 图片完整性验证：MinIO存储文件的可访问性
 * 3. 缩略图完整性检查：验证所有尺寸的缩略图
 * 4. 自动修复功能：重新下载、URL更新、缩略图生成
 * 5. 详细报告生成：统计信息和修复建议
 */

require('dotenv').config();
const mongoose = require('mongoose');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// 导入模型和服务
const { Product } = require('./dist/models/Product');
const { Image } = require('./dist/models/Image');
const { ImageService } = require('./dist/services/imageService');
const { getFeishuApiService } = require('./dist/services/feishuApiService');

// 配置选项
const CONFIG = {
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  autoRepair: process.argv.includes('--auto-repair'),
  skipThumbnails: process.argv.includes('--skip-thumbnails'),
  maxConcurrency: 5,
  timeout: 30000,
  reportPath: `./reports/image-consistency-report-${Date.now()}.json`
};

// 检查结果统计
const stats = {
  totalProducts: 0,
  totalImages: 0,
  checkedProducts: 0,
  checkedImages: 0,
  issues: {
    missingImageRecords: 0,
    missingFiles: 0,
    brokenUrls: 0,
    missingThumbnails: 0,
    urlMismatches: 0,
    orphanedRecords: 0
  },
  repairs: {
    attempted: 0,
    successful: 0,
    failed: 0
  },
  errors: []
};

// 详细检查结果
const detailedResults = {
  productChecks: [],
  imageChecks: [],
  repairActions: [],
  recommendations: []
};

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: false,
      w: 1,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    throw error;
  }
}

/**
 * 检查HTTP URL的可访问性
 */
async function checkUrlAccessibility(url, timeout = CONFIG.timeout) {
  try {
    const response = await axios.head(url, { 
      timeout,
      validateStatus: (status) => status < 400
    });
    return {
      accessible: true,
      status: response.status,
      size: response.headers['content-length'] ? parseInt(response.headers['content-length']) : null
    };
  } catch (error) {
    return {
      accessible: false,
      error: error.message,
      status: error.response?.status || null
    };
  }
}

/**
 * 检查产品图片的数据关联性
 */
async function checkProductImageConsistency(product) {
  const productCheck = {
    productId: product.productId,
    name: product.name,
    issues: [],
    imageChecks: {}
  };

  const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
  
  for (const imageType of imageTypes) {
    const productImageUrl = product.images?.[imageType];
    
    if (productImageUrl) {
      const imageCheck = await checkSingleImageConsistency(
        product.productId, 
        imageType, 
        productImageUrl
      );
      
      productCheck.imageChecks[imageType] = imageCheck;
      
      // 统计问题
      if (imageCheck.issues.length > 0) {
        productCheck.issues.push(...imageCheck.issues.map(issue => `${imageType}: ${issue}`));
      }
    }
  }

  return productCheck;
}

/**
 * 检查单个图片的一致性
 */
async function checkSingleImageConsistency(productId, imageType, productImageUrl) {
  const check = {
    productId,
    imageType,
    productImageUrl,
    issues: [],
    imageRecord: null,
    urlAccessible: null,
    thumbnails: {}
  };

  try {
    // 1. 检查Image表记录是否存在
    const imageRecord = await Image.findOne({ 
      productId, 
      type: imageType,
      isActive: true 
    });

    if (!imageRecord) {
      check.issues.push('缺少Image表记录');
      stats.issues.missingImageRecords++;
    } else {
      check.imageRecord = {
        imageId: imageRecord.imageId,
        publicUrl: imageRecord.publicUrl,
        objectName: imageRecord.objectName,
        thumbnails: imageRecord.thumbnails
      };

      // 2. 检查URL一致性
      if (productImageUrl !== imageRecord.publicUrl) {
        check.issues.push(`URL不匹配: Product表=${productImageUrl}, Image表=${imageRecord.publicUrl}`);
        stats.issues.urlMismatches++;
      }

      // 3. 检查图片文件可访问性
      const accessibility = await checkUrlAccessibility(imageRecord.publicUrl);
      check.urlAccessible = accessibility;
      
      if (!accessibility.accessible) {
        check.issues.push(`图片文件不可访问: ${accessibility.error}`);
        stats.issues.brokenUrls++;
      }

      // 4. 检查缩略图完整性（如果不跳过）
      if (!CONFIG.skipThumbnails && imageRecord.thumbnails) {
        const thumbnailSizes = ['small', 'medium', 'large'];
        
        for (const size of thumbnailSizes) {
          if (imageRecord.thumbnails[size]) {
            const thumbnailUrl = imageRecord.thumbnails[size];
            const thumbnailAccessibility = await checkUrlAccessibility(thumbnailUrl);
            
            check.thumbnails[size] = thumbnailAccessibility;
            
            if (!thumbnailAccessibility.accessible) {
              check.issues.push(`${size}缩略图不可访问: ${thumbnailAccessibility.error}`);
              stats.issues.missingThumbnails++;
            }
          } else {
            check.issues.push(`缺少${size}缩略图`);
            stats.issues.missingThumbnails++;
          }
        }
      }
    }

    stats.checkedImages++;
    
  } catch (error) {
    check.issues.push(`检查失败: ${error.message}`);
    stats.errors.push(`检查图片失败 ${productId}/${imageType}: ${error.message}`);
  }

  return check;
}

/**
 * 检查孤立的Image记录
 */
async function checkOrphanedImageRecords() {
  console.log('🔍 检查孤立的Image记录...');
  
  try {
    const imageRecords = await Image.find({ isActive: true });
    const orphanedRecords = [];

    for (const imageRecord of imageRecords) {
      const product = await Product.findOne({ productId: imageRecord.productId });
      
      if (!product) {
        orphanedRecords.push({
          imageId: imageRecord.imageId,
          productId: imageRecord.productId,
          type: imageRecord.type,
          reason: '产品记录不存在'
        });
        stats.issues.orphanedRecords++;
      } else {
        // 检查产品是否引用了这个图片
        const productImageUrl = product.images?.[imageRecord.type];
        if (!productImageUrl || productImageUrl !== imageRecord.publicUrl) {
          orphanedRecords.push({
            imageId: imageRecord.imageId,
            productId: imageRecord.productId,
            type: imageRecord.type,
            reason: '产品未引用此图片'
          });
          stats.issues.orphanedRecords++;
        }
      }
    }

    detailedResults.orphanedRecords = orphanedRecords;
    console.log(`📊 发现 ${orphanedRecords.length} 个孤立的Image记录`);
    
  } catch (error) {
    console.error('❌ 检查孤立记录失败:', error);
    stats.errors.push(`检查孤立记录失败: ${error.message}`);
  }
}

/**
 * 主检查函数
 */
async function performConsistencyCheck() {
  console.log('🚀 开始图片数据一致性检查...');
  console.log(`配置: dryRun=${CONFIG.dryRun}, autoRepair=${CONFIG.autoRepair}, skipThumbnails=${CONFIG.skipThumbnails}`);

  try {
    // 获取所有产品
    const products = await Product.find({}).lean();
    stats.totalProducts = products.length;
    
    // 统计总图片数
    for (const product of products) {
      if (product.images) {
        stats.totalImages += Object.values(product.images).filter(Boolean).length;
      }
    }

    console.log(`📊 找到 ${stats.totalProducts} 个产品，${stats.totalImages} 张图片`);

    // 检查每个产品的图片一致性
    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      
      if (CONFIG.verbose) {
        console.log(`\n📦 检查产品 ${i + 1}/${products.length}: ${product.productId}`);
      }

      const productCheck = await checkProductImageConsistency(product);
      detailedResults.productChecks.push(productCheck);
      
      stats.checkedProducts++;
      
      // 显示进度
      if ((i + 1) % 10 === 0) {
        console.log(`📈 进度: ${i + 1}/${products.length} (${Math.round((i + 1) / products.length * 100)}%)`);
      }
    }

    // 检查孤立的Image记录
    await checkOrphanedImageRecords();

    console.log('\n✅ 一致性检查完成');

  } catch (error) {
    console.error('❌ 一致性检查失败:', error);
    stats.errors.push(`一致性检查失败: ${error.message}`);
  }
}

/**
 * 自动修复功能
 */
async function performAutoRepair() {
  if (!CONFIG.autoRepair) {
    console.log('⚠️  自动修复功能未启用，跳过修复步骤');
    return;
  }

  console.log('\n🔧 开始自动修复...');

  const imageService = new ImageService();
  const feishuService = getFeishuApiService();

  // 修复有问题的产品图片
  for (const productCheck of detailedResults.productChecks) {
    if (productCheck.issues.length === 0) continue;

    console.log(`\n🔧 修复产品: ${productCheck.productId}`);

    for (const [imageType, imageCheck] of Object.entries(productCheck.imageChecks)) {
      if (imageCheck.issues.length === 0) continue;

      const repairAction = {
        productId: productCheck.productId,
        imageType,
        issues: imageCheck.issues,
        actions: [],
        success: false,
        error: null
      };

      try {
        stats.repairs.attempted++;

        // 1. 如果缺少Image记录，尝试从飞书重新下载
        if (imageCheck.issues.some(issue => issue.includes('缺少Image表记录'))) {
          console.log(`  📥 重新从飞书下载 ${imageType} 图片...`);

          // 查找飞书文件令牌（需要从产品的原始数据中获取）
          const product = await Product.findOne({ productId: productCheck.productId });
          if (product && product.metadata?.feishuData) {
            const feishuData = product.metadata.feishuData;
            const imageFieldMap = {
              front: 'Front image(正)',
              back: 'Back image(背)',
              label: 'Tag photo(标签)',
              package: 'Outer packaging image(外包装)',
              gift: 'Gift pictures(赠品图片)'
            };

            const fieldName = imageFieldMap[imageType];
            const fieldValue = feishuData[fieldName];

            if (fieldValue && Array.isArray(fieldValue) && fieldValue.length > 0) {
              const fileToken = fieldValue[0].file_token || fieldValue[0];

              if (fileToken) {
                const imageRecord = await imageService.downloadFromFeishu(
                  fileToken,
                  productCheck.productId,
                  imageType
                );

                // 更新产品记录
                await Product.updateOne(
                  { productId: productCheck.productId },
                  {
                    $set: {
                      [`images.${imageType}`]: imageRecord.publicUrl,
                      updatedAt: new Date()
                    }
                  }
                );

                repairAction.actions.push('重新下载图片并更新记录');
                repairAction.success = true;
                stats.repairs.successful++;
              }
            }
          }
        }

        // 2. 如果URL不匹配，更新产品记录
        else if (imageCheck.issues.some(issue => issue.includes('URL不匹配'))) {
          console.log(`  🔄 更新 ${imageType} 图片URL引用...`);

          const imageRecord = await Image.findOne({
            productId: productCheck.productId,
            type: imageType,
            isActive: true
          });

          if (imageRecord) {
            await Product.updateOne(
              { productId: productCheck.productId },
              {
                $set: {
                  [`images.${imageType}`]: imageRecord.publicUrl,
                  updatedAt: new Date()
                }
              }
            );

            repairAction.actions.push('更新产品图片URL引用');
            repairAction.success = true;
            stats.repairs.successful++;
          }
        }

        // 3. 如果图片文件不可访问，尝试重新下载
        else if (imageCheck.issues.some(issue => issue.includes('图片文件不可访问'))) {
          console.log(`  🔄 重新下载不可访问的 ${imageType} 图片...`);

          const imageRecord = await Image.findOne({
            productId: productCheck.productId,
            type: imageType,
            isActive: true
          });

          if (imageRecord && imageRecord.metadata?.feishuFileToken) {
            try {
              const imageBuffer = await feishuService.downloadImage(imageRecord.metadata.feishuFileToken);

              // 重新上传到MinIO
              const newImageRecord = await imageService.uploadImage(
                imageBuffer,
                imageRecord.originalName,
                productCheck.productId,
                imageType
              );

              repairAction.actions.push('重新下载并上传图片文件');
              repairAction.success = true;
              stats.repairs.successful++;
            } catch (downloadError) {
              repairAction.error = `重新下载失败: ${downloadError.message}`;
            }
          }
        }

        if (!repairAction.success) {
          stats.repairs.failed++;
          repairAction.error = repairAction.error || '无法自动修复';
        }

      } catch (error) {
        stats.repairs.failed++;
        repairAction.error = error.message;
        console.error(`  ❌ 修复失败: ${error.message}`);
      }

      detailedResults.repairActions.push(repairAction);
    }
  }

  console.log(`\n✅ 自动修复完成: 尝试 ${stats.repairs.attempted}, 成功 ${stats.repairs.successful}, 失败 ${stats.repairs.failed}`);
}

/**
 * 生成建议
 */
function generateRecommendations() {
  const recommendations = [];

  // 基于统计数据生成建议
  if (stats.issues.missingImageRecords > 0) {
    recommendations.push({
      type: 'missing_records',
      priority: 'high',
      description: `发现 ${stats.issues.missingImageRecords} 个缺少Image表记录的图片`,
      action: '运行 update-images-from-feishu.js 重新同步图片数据',
      command: 'node update-images-from-feishu.js'
    });
  }

  if (stats.issues.brokenUrls > 0) {
    recommendations.push({
      type: 'broken_urls',
      priority: 'critical',
      description: `发现 ${stats.issues.brokenUrls} 个不可访问的图片URL`,
      action: '检查MinIO服务状态，或重新下载图片',
      command: 'node comprehensive-image-consistency-check.js --auto-repair'
    });
  }

  if (stats.issues.urlMismatches > 0) {
    recommendations.push({
      type: 'url_mismatches',
      priority: 'medium',
      description: `发现 ${stats.issues.urlMismatches} 个URL不匹配的情况`,
      action: '运行产品图片引用更新脚本',
      command: 'node update-product-images.js'
    });
  }

  if (stats.issues.missingThumbnails > 0) {
    recommendations.push({
      type: 'missing_thumbnails',
      priority: 'low',
      description: `发现 ${stats.issues.missingThumbnails} 个缺失的缩略图`,
      action: '重新生成缩略图',
      command: 'node scripts/regenerate-thumbnails.js'
    });
  }

  if (stats.issues.orphanedRecords > 0) {
    recommendations.push({
      type: 'orphaned_records',
      priority: 'medium',
      description: `发现 ${stats.issues.orphanedRecords} 个孤立的Image记录`,
      action: '清理孤立记录',
      command: 'node scripts/cleanup-orphaned-images.js'
    });
  }

  // 性能建议
  if (stats.totalImages > 1000) {
    recommendations.push({
      type: 'performance',
      priority: 'low',
      description: '图片数量较多，建议启用CDN加速',
      action: '配置CDN服务以提高图片加载速度'
    });
  }

  detailedResults.recommendations = recommendations;
  return recommendations;
}

/**
 * 生成详细报告
 */
async function generateReport() {
  console.log('\n📊 生成详细报告...');

  const report = {
    timestamp: new Date().toISOString(),
    config: CONFIG,
    summary: {
      totalProducts: stats.totalProducts,
      totalImages: stats.totalImages,
      checkedProducts: stats.checkedProducts,
      checkedImages: stats.checkedImages,
      issuesFound: Object.values(stats.issues).reduce((sum, count) => sum + count, 0),
      repairsAttempted: stats.repairs.attempted,
      repairsSuccessful: stats.repairs.successful,
      repairsFailed: stats.repairs.failed
    },
    statistics: stats,
    detailedResults,
    recommendations: generateRecommendations()
  };

  // 确保报告目录存在
  const reportDir = path.dirname(CONFIG.reportPath);
  try {
    await fs.mkdir(reportDir, { recursive: true });
  } catch (error) {
    // 目录可能已存在，忽略错误
  }

  // 保存详细报告到文件
  try {
    await fs.writeFile(CONFIG.reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 详细报告已保存到: ${CONFIG.reportPath}`);
  } catch (error) {
    console.error('❌ 保存报告失败:', error);
  }

  // 在控制台显示摘要报告
  console.log('\n' + '='.repeat(60));
  console.log('📊 图片数据一致性检查报告');
  console.log('='.repeat(60));

  console.log('\n📈 统计摘要:');
  console.log(`  总产品数: ${stats.totalProducts}`);
  console.log(`  总图片数: ${stats.totalImages}`);
  console.log(`  已检查产品: ${stats.checkedProducts}`);
  console.log(`  已检查图片: ${stats.checkedImages}`);

  console.log('\n⚠️  发现的问题:');
  console.log(`  缺少Image记录: ${stats.issues.missingImageRecords}`);
  console.log(`  图片文件不可访问: ${stats.issues.brokenUrls}`);
  console.log(`  URL不匹配: ${stats.issues.urlMismatches}`);
  console.log(`  缺少缩略图: ${stats.issues.missingThumbnails}`);
  console.log(`  孤立记录: ${stats.issues.orphanedRecords}`);

  if (CONFIG.autoRepair) {
    console.log('\n🔧 修复结果:');
    console.log(`  尝试修复: ${stats.repairs.attempted}`);
    console.log(`  修复成功: ${stats.repairs.successful}`);
    console.log(`  修复失败: ${stats.repairs.failed}`);
  }

  console.log('\n💡 建议操作:');
  const recommendations = report.recommendations;
  if (recommendations.length === 0) {
    console.log('  ✅ 未发现需要处理的问题');
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.description}`);
      console.log(`     操作: ${rec.action}`);
      if (rec.command) {
        console.log(`     命令: ${rec.command}`);
      }
      console.log('');
    });
  }

  if (stats.errors.length > 0) {
    console.log('\n❌ 错误信息:');
    stats.errors.slice(0, 10).forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
    if (stats.errors.length > 10) {
      console.log(`  ... 还有 ${stats.errors.length - 10} 个错误，详见报告文件`);
    }
  }

  console.log('\n' + '='.repeat(60));

  return report;
}

/**
 * 主函数
 */
async function main() {
  const startTime = Date.now();

  try {
    console.log('🚀 图片数据一致性检查和修复工具');
    console.log('='.repeat(50));

    // 显示配置信息
    console.log('\n⚙️  配置信息:');
    console.log(`  干运行模式: ${CONFIG.dryRun ? '是' : '否'}`);
    console.log(`  详细输出: ${CONFIG.verbose ? '是' : '否'}`);
    console.log(`  自动修复: ${CONFIG.autoRepair ? '是' : '否'}`);
    console.log(`  跳过缩略图检查: ${CONFIG.skipThumbnails ? '是' : '否'}`);
    console.log(`  并发数: ${CONFIG.maxConcurrency}`);
    console.log(`  超时时间: ${CONFIG.timeout}ms`);

    // 连接数据库
    await connectDatabase();

    // 执行一致性检查
    await performConsistencyCheck();

    // 执行自动修复（如果启用）
    if (!CONFIG.dryRun) {
      await performAutoRepair();
    }

    // 生成报告
    await generateReport();

    const duration = Date.now() - startTime;
    console.log(`\n⏱️  总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('✅ 检查完成！');

  } catch (error) {
    console.error('\n❌ 程序执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('📝 数据库连接已关闭');
    }
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  performConsistencyCheck,
  performAutoRepair,
  generateReport,
  CONFIG,
  stats
};
